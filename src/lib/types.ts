// Core type definitions for ApexPro

export interface User {
  id: string;
  email: string;
  role: 'Member' | 'InstitutionRep' | 'Admin';
  createdAt: Date;
  updatedAt: Date;
}

export interface Member {
  id: string;
  userId: string;
  associationId?: string;
  title: string;
  firstName: string;
  lastName: string;
  gender: 'Male' | 'Female' | 'Other';
  ghanaCardNo: string;
  dateOfBirth: Date;
  profession: string;
  company?: string;
  address: string;
  phone: string;
  dateOfAdmission: Date;
  ipLicenseNo?: string;
  membershipTierId: string;
  status: 'Active' | 'Expired' | 'Pending' | 'Suspended';
  createdAt: Date;
  updatedAt: Date;
}

export interface Institution {
  id: string;
  name: string;
  address: string;
  corporateTierId: string;
  primaryContactId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface MembershipTier {
  id: string;
  name: string;
  type: 'Individual' | 'Corporate';
  subscriptionFee: number;
  staffLimit?: number;
  benefitsDescription: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Subscription {
  id: string;
  memberId?: string;
  institutionId?: string;
  tierId: string;
  startDate: Date;
  endDate: Date;
  status: 'Active' | 'Expired' | 'Pending';
  createdAt: Date;
  updatedAt: Date;
}

export interface Invoice {
  id: string;
  memberId?: string;
  institutionId?: string;
  amount: number;
  dueDate: Date;
  status: 'Paid' | 'Unpaid' | 'Overdue';
  paymentId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Payment {
  id: string;
  invoiceId: string;
  amount: number;
  paymentDate: Date;
  method: 'Online' | 'Offline';
  gatewayTransactionId?: string;
  offlineProofUrl?: string;
  status: 'Completed' | 'Pending_Reconciliation' | 'Failed';
  createdAt: Date;
  updatedAt: Date;
}

export interface CPDRecord {
  id: string;
  memberId: string;
  year: number;
  title: string;
  description: string;
  hours: number;
  date: Date;
  evidenceUrl?: string;
  status: 'Logged' | 'Declared';
  createdAt: Date;
  updatedAt: Date;
}

export interface Document {
  id: string;
  memberId: string;
  type: 'Certificate' | 'GoodStanding' | 'License' | 'Application';
  fileUrl: string;
  createdAt: Date;
  updatedAt: Date;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

// Form types
export interface MembershipApplicationForm {
  // Personal Information
  title: string;
  firstName: string;
  lastName: string;
  gender: 'Male' | 'Female' | 'Other';
  ghanaCardNo: string;
  dateOfBirth: string;
  
  // Professional Information
  profession: string;
  company?: string;
  ipLicenseNo?: string;
  
  // Contact Information
  address: string;
  phone: string;
  email: string;
  
  // Membership Information
  membershipTierId: string;
  
  // Documents
  documents: File[];
}

export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
}

// Dashboard types
export interface MemberDashboardData {
  member: Member;
  subscription: Subscription;
  membershipTier: MembershipTier;
  pendingInvoices: Invoice[];
  recentPayments: Payment[];
  cpdStatus: {
    currentYear: number;
    totalHours: number;
    requiredHours: number;
    declarations: CPDRecord[];
  };
  announcements: Announcement[];
}

export interface AdminDashboardData {
  totalMembers: number;
  pendingApplications: number;
  revenueThisMonth: number;
  expiredSubscriptions: number;
  recentActivities: Activity[];
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  priority: 'Low' | 'Medium' | 'High';
  targetAudience: 'All' | 'Members' | 'Institutions' | 'Admins';
  createdAt: Date;
  expiresAt?: Date;
}

export interface Activity {
  id: string;
  type: string;
  description: string;
  userId: string;
  createdAt: Date;
}
