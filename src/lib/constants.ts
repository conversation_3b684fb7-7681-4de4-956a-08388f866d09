// Application constants for ApexPro

export const APP_NAME = 'ApexPro';
export const APP_DESCRIPTION = 'Professional Membership Portal';

// API Routes
export const API_ROUTES = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    PROFILE: '/api/auth/profile'
  },
  MEMBERS: {
    BASE: '/api/members',
    APPLICATION: '/api/members/application',
    PROFILE: '/api/members/profile',
    CPD: '/api/members/cpd',
    DOCUMENTS: '/api/members/documents'
  },
  INSTITUTIONS: {
    BASE: '/api/institutions',
    STAFF: '/api/institutions/staff',
    BILLING: '/api/institutions/billing'
  },
  ADMIN: {
    DASHBOARD: '/api/admin/dashboard',
    MEMBERS: '/api/admin/members',
    INSTITUTIONS: '/api/admin/institutions',
    APPLICATIONS: '/api/admin/applications',
    REPORTS: '/api/admin/reports'
  },
  PAYMENTS: {
    BASE: '/api/payments',
    STRIPE: '/api/payments/stripe',
    RECONCILE: '/api/payments/reconcile'
  }
} as const;

// User Roles
export const USER_ROLES = {
  MEMBER: 'Member',
  INSTITUTION_REP: 'InstitutionRep',
  ADMIN: 'Admin'
} as const;

// Membership Statuses
export const MEMBERSHIP_STATUS = {
  ACTIVE: 'Active',
  EXPIRED: 'Expired',
  PENDING: 'Pending',
  SUSPENDED: 'Suspended'
} as const;

// Payment Statuses
export const PAYMENT_STATUS = {
  PAID: 'Paid',
  UNPAID: 'Unpaid',
  OVERDUE: 'Overdue',
  COMPLETED: 'Completed',
  PENDING_RECONCILIATION: 'Pending_Reconciliation',
  FAILED: 'Failed'
} as const;

// CPD Requirements
export const CPD_REQUIREMENTS = {
  ANNUAL_HOURS: 40,
  DECLARATION_DEADLINE: '31-01' // January 31st
} as const;

// File Upload Limits
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'],
  MAX_FILES: 5
} as const;

// Membership Tiers
export const MEMBERSHIP_TIERS = {
  INDIVIDUAL: {
    HONORARY: 'Honorary',
    FULL: 'Full Member',
    ASSOCIATE: 'Associate'
  },
  CORPORATE: {
    BRONZE: 'Bronze',
    SILVER: 'Silver',
    GOLD: 'Gold',
    PLATINUM: 'Platinum'
  }
} as const;

// Navigation Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  APPLICATION: '/application',
  MEMBER_DASHBOARD: '/dashboard/member',
  INSTITUTION_DASHBOARD: '/dashboard/institution',
  ADMIN_DASHBOARD: '/dashboard/admin',
  PROFILE: '/profile',
  CPD: '/cpd',
  PAYMENTS: '/payments',
  DOCUMENTS: '/documents'
} as const;

// Form Validation Messages
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PHONE: 'Please enter a valid Ghana phone number',
  INVALID_GHANA_CARD: 'Please enter a valid Ghana Card number (GHA-XXXXXXXXX-X)',
  PASSWORD_MIN_LENGTH: 'Password must be at least 8 characters',
  PASSWORD_MISMATCH: 'Passwords do not match',
  FILE_TOO_LARGE: 'File size must be less than 5MB',
  INVALID_FILE_TYPE: 'Invalid file type. Please upload PDF, JPG, PNG, DOC, or DOCX files'
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'DD/MM/YYYY',
  INPUT: 'YYYY-MM-DD',
  DATETIME: 'DD/MM/YYYY HH:mm'
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 25, 50, 100]
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'apex_pro_token',
  USER_PREFERENCES: 'apex_pro_preferences',
  DRAFT_APPLICATION: 'apex_pro_draft_application'
} as const;
