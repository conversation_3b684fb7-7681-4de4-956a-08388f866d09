import { NextRequest } from 'next/server';
import { verifyToken, extractTokenFromHeader, JWTPayload } from '@/lib/auth';
import { ApiError } from './response';

export interface AuthenticatedRequest extends NextRequest {
  user?: JWTPayload;
}

export const authenticateToken = (request: NextRequest): JWTPayload => {
  const authHeader = request.headers.get('authorization');
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    throw new ApiError(401, 'Access token is required');
  }

  const payload = verifyToken(token);
  if (!payload) {
    throw new ApiError(401, 'Invalid or expired token');
  }

  return payload;
};

export const requireRole = (allowedRoles: string[]) => {
  return (user: JWTPayload): void => {
    if (!allowedRoles.includes(user.role)) {
      throw new ApiError(403, 'Insufficient permissions');
    }
  };
};

export const requireAdmin = requireRole(['Admin']);
export const requireMemberOrAdmin = requireRole(['Member', 'Admin']);
export const requireInstitutionRepOrAdmin = requireRole(['InstitutionRep', 'Admin']);

export const withAuth = (
  handler: (request: NextRequest, user: JWTPayload) => Promise<Response>,
  roleCheck?: (user: JWTPayload) => void
) => {
  return async (request: NextRequest): Promise<Response> => {
    try {
      const user = authenticateToken(request);
      
      if (roleCheck) {
        roleCheck(user);
      }

      return await handler(request, user);
    } catch (error) {
      if (error instanceof ApiError) {
        return new Response(
          JSON.stringify({
            success: false,
            message: error.message,
            errors: error.errors,
          }),
          {
            status: error.statusCode,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }

      return new Response(
        JSON.stringify({
          success: false,
          message: 'Internal server error',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
  };
};
